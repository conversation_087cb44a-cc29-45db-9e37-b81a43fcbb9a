import request from '@/utils/http'

/**
 * 登录接口
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {string} code 验证码
 * @param {string} uuid 验证码UUID
 */
export function login(username, password, code, uuid) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: { username, password, code, uuid },
    custom: {
      auth: false // 登录接口不需要认证
    }
  })
}

/**
 * 获取验证码
 */
export function getCodeImg() {
  return request({
    url: '/code',
    method: 'get',
    custom: {
      auth: false // 验证码接口不需要认证
    }
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

/**
 * 退出登录
 */
export function logout() {
  return request({
    url: '/auth/TokenLogout',
    method: 'delete'
  })
}

/**
 * 刷新token
 */
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}
