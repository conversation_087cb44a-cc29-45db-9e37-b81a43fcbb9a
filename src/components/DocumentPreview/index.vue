<template>
  <view class="document-preview">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">文档加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-icon">📄</view>
      <text class="error-text">{{ error }}</text>
      <button class="retry-btn" @click="retryLoad">重试</button>
    </view>

    <!-- PDF预览 -->
    <view v-else-if="fileType === 'pdf'" class="pdf-preview">
      <VueOfficePdf
        :src="fileUrl"
        style="height: 100%;"
        @rendered="handlePdfRendered"
        @error="handlePdfError"
      />
    </view>

    <!-- Word文档预览 -->
    <view v-else-if="isWordFile" class="word-preview">
      <VueOfficeDocx
        :src="fileUrl"
        style="height: 100%;"
        @rendered="handleDocxRendered"
        @error="handleDocxError"
      />
    </view>

    <!-- Excel文档预览 -->
    <view v-else-if="isExcelFile" class="excel-preview">
      <VueOfficeExcel
        :src="fileUrl"
        style="height: 100%;"
        @rendered="handleExcelRendered"
        @error="handleExcelError"
      />
    </view>

    <!-- 图片预览 -->
    <view v-else-if="isImageFile" class="image-preview">
      <image :src="fileUrl" mode="aspectFit" class="preview-image" @error="handleImageError" />
    </view>

    <!-- 文本文件预览 -->
    <view v-else-if="isTextFile" class="text-preview">
      <scroll-view scroll-y class="text-content">
        <text class="text-body">{{ textContent }}</text>
      </scroll-view>
    </view>

    <!-- 不支持的文件类型 -->
    <view v-else class="unsupported-container">
      <view class="unsupported-icon">📎</view>
      <text class="unsupported-text">暂不支持预览此类型文件</text>
      <text class="file-info">{{ fileName }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import VueOfficePdf from '@vue-office/pdf'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficeExcel from '@vue-office/excel'

interface Props {
  fileUrl: string
  fileName?: string
  fileType?: string
  kkFileViewUrl?: string // kkFileView服务地址
}

const props = withDefaults(defineProps<Props>(), {
  fileName: '',
  fileType: '',
  kkFileViewUrl: ''
})

const loading = ref(true)
const error = ref('')
const textContent = ref('')

// 文件类型判断
const fileExtension = computed(() => {
  if (props.fileType) return props.fileType.toLowerCase()
  const ext = props.fileName.split('.').pop()?.toLowerCase() || ''
  return ext
})

const isWordFile = computed(() => {
  const wordExts = ['doc', 'docx']
  return wordExts.includes(fileExtension.value)
})

const isExcelFile = computed(() => {
  const excelExts = ['xls', 'xlsx']
  return excelExts.includes(fileExtension.value)
})

const isPptFile = computed(() => {
  const pptExts = ['ppt', 'pptx']
  return pptExts.includes(fileExtension.value)
})

const isImageFile = computed(() => {
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  return imageExts.includes(fileExtension.value)
})

const isTextFile = computed(() => {
  const textExts = ['txt', 'md', 'json', 'xml', 'csv']
  return textExts.includes(fileExtension.value)
})

// vue-office事件处理函数
const handlePdfRendered = () => {
  loading.value = false
  console.log('PDF渲染完成')
}

const handlePdfError = (err: any) => {
  loading.value = false
  error.value = 'PDF文档加载失败'
  console.error('PDF加载错误:', err)
}

const handleDocxRendered = () => {
  loading.value = false
  console.log('Word文档渲染完成')
}

const handleDocxError = (err: any) => {
  loading.value = false
  error.value = 'Word文档加载失败'
  console.error('Word加载错误:', err)
}

const handleExcelRendered = () => {
  loading.value = false
  console.log('Excel文档渲染完成')
}

const handleExcelError = (err: any) => {
  loading.value = false
  error.value = 'Excel文档加载失败'
  console.error('Excel加载错误:', err)
}

// 加载文档
const loadDocument = async () => {
  loading.value = true
  error.value = ''

  try {
    if (fileExtension.value === 'pdf' || isWordFile.value || isExcelFile.value) {
      // PDF、Word、Excel文件使用vue-office组件
      // 先设置一个超时，如果组件事件没有触发，就显示错误
      setTimeout(() => {
        if (loading.value) {
          loading.value = false
          error.value = '文档加载超时，请检查文件链接是否有效'
        }
      }, 10000) // 10秒超时
    } else if (isImageFile.value) {
      // 图片文件直接显示
      loading.value = false
    } else if (isTextFile.value) {
      // 文本文件需要获取内容
      await loadTextContent()
    } else {
      // 不支持的文件类型
      loading.value = false
    }
  } catch (err) {
    error.value = err.message || '文档加载失败'
    loading.value = false
  }
}

// 加载文本内容
const loadTextContent = async () => {
  try {
    const response = await uni.request({
      url: props.fileUrl,
      method: 'GET'
    })
    
    if (response.statusCode === 200) {
      textContent.value = response.data
    } else {
      throw new Error('文件加载失败')
    }
  } catch (err) {
    throw new Error('无法加载文本内容')
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  loadDocument()
}

// 处理图片加载错误
const handleImageError = () => {
  error.value = '图片加载失败'
}




// 监听文件URL变化
watch(() => props.fileUrl, () => {
  if (props.fileUrl) {
    loadDocument()
  }
}, { immediate: true })

onMounted(() => {
  if (props.fileUrl) {
    loadDocument()
  }
})
</script>

<style scoped lang="scss">
.document-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.loading-container,
.error-container,
.unsupported-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e4e7ed;
  border-top: 4rpx solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-text,
.unsupported-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 20rpx;
}

.error-icon,
.unsupported-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.file-info {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
  text-align: center;
  word-break: break-all;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.pdf-preview,
.word-preview,
.excel-preview {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
}

.text-preview {
  flex: 1;
  padding: 20rpx;
}

.text-content {
  height: 100%;
  background-color: white;
  border-radius: 8rpx;
  padding: 20rpx;
}

.text-body {
  font-size: 26rpx;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
