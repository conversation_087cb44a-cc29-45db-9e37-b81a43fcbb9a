import develop from '@/config/develop'
import release from '@/config/release'
import trial from '@/config/trial'

const envs = {
  develop,
  trial,
  release,
}

// 条件编译：APP-PLUS 环境下强制使用 release 配置
let env
// #ifdef APP-PLUS
env = 'release'
// #endif

// #ifndef APP-PLUS
// 在开发环境下使用 develop 配置，其他环境使用 release 配置
env = process.env.NODE_ENV === 'development' ? 'develop' : 'release'
// #endif

const config = envs[env]

export default {
  ...config,
  env,
}