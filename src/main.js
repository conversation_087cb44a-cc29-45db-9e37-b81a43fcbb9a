import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

// 引入 vue-office 组件
import VueOfficePdf from '@vue-office/pdf'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficeExcel from '@vue-office/excel'

// 引入 v-md-editor 预览组件
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import '@kangc/v-md-editor/lib/style/preview.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'

// 引入代码高亮
import hljs from 'highlight.js'

// 引入 mermaid
import mermaid from 'mermaid'
import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/npm'
import '@kangc/v-md-editor/lib/plugins/mermaid/mermaid.css'

// 配置 mermaid
mermaid.initialize({
  startOnLoad: false,
  theme: 'default',
  securityLevel: 'loose',
  fontFamily: 'Arial, sans-serif'
})

// 配置 v-md-editor
VMdPreview.use(githubTheme, {
  Hljs: hljs,
})
VMdPreview.use(createMermaidPlugin({ mermaid }))

export function createApp() {
	const app = createSSRApp(App)
	const pinia = createPinia()

	app.use(pinia)
	app.use(VMdPreview)

	// 全局注册 vue-office 组件
	app.component('VueOfficePdf', VueOfficePdf)
	app.component('VueOfficeDocx', VueOfficeDocx)
	app.component('VueOfficeExcel', VueOfficeExcel)

	return {
		app,
	}
}
