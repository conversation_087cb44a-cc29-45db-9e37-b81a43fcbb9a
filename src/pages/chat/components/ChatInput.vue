<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  isSending: boolean
  safeAreaBottom: number
}

defineProps<Props>()

const emit = defineEmits<{
  send: [text: string]
}>()

const inputText = ref('')

const sendMessage = () => {
  if (!inputText.value.trim()) return
  
  const text = inputText.value.trim()
  inputText.value = ''
  emit('send', text)
}
</script>

<template>
  <view class="input-area">
    <view class="input-wrapper">
      <view class="input-container">
        <textarea
          v-model="inputText"
          class="input-field"
          placeholder="输入您的问题..."
          :disabled="isSending"
          auto-height
          :maxlength="1000"
          @confirm="sendMessage"
        />
      </view>
      <view class="action-buttons">
        <button
          class="send-button"
          :class="{ 'sending': isSending, 'disabled': !inputText.trim() }"
          @click="sendMessage"
          :disabled="isSending || !inputText.trim()"
        >
          <text class="send-icon">{{ isSending ? '⏸' : '→' }}</text>
        </button>
      </view>
    </view>
    <!-- 安全区域占位 -->
    <view class="safe-area" :style="{ height: safeAreaBottom + 'px' }"></view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: $input-area-bg;
  border-top: 1rpx solid $border-color;
  padding: $spacing-md;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: $spacing-md;
}

.input-container {
  flex: 1;
  background-color: #F8F9FA;
  border: 1rpx solid $border-color;
  border-radius: 24rpx;
  padding: $spacing-lg;
  transition: $transition-fast;
  min-height: 80rpx;
  display: flex;
  align-items: center;

  &:focus-within {
    border-color: $accent-color;
    box-shadow: 0 0 0 2rpx rgba(20, 91, 255, 0.1);
  }
}

.input-field {
  width: 100%;
  min-height: 40rpx;
  max-height: $input-max-height;
  font-size: 32rpx;
  color: $text-primary;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  line-height: 1.4;

  &::placeholder {
    color: $placeholder-text;
    font-size: 30rpx;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
}

.send-button {
  width: $send-button-size;
  height: $send-button-size;
  background-color: $accent-color;
  border: none;
  border-radius: $border-radius-circle;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition-fast;

  &:not(.disabled):active {
    background-color: #0d47d9;
    transform: scale(0.95);
  }

  &.disabled {
    background-color: #D1D5DB;
    opacity: 0.6;
  }

  &.sending {
    background-color: #FF4757;
  }
}

.send-icon {
  font-size: 32rpx;
  color: #FFFFFF;
  font-weight: bold;
}

.safe-area {
  background-color: $input-area-bg;
  min-height: 20rpx; /* 最小安全区域高度 */
}
</style>
