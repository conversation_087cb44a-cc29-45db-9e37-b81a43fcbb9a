<script setup lang="ts">
interface Props {
  statusBarHeight: number
  title: string
}

defineProps<Props>()

const emit = defineEmits<{
  showHistory: []
  newChat: []
}>()

const handleShowHistory = () => {
  emit('showHistory')
}

const handleNewChat = () => {
  emit('newChat')
}
</script>

<template>
  <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="navbar-content">
      <view class="navbar-left" @click="handleShowHistory">
        <text class="history-icon">☰</text>
      </view>
      <view class="navbar-center">
        <text class="navbar-title">{{ title || '智能对话' }}</text>
      </view>
      <view class="navbar-right">
        <view class="nav-button" @click="handleNewChat">
          <text class="new-chat-icon">✨</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.custom-navbar {
  background-color: $header-bg-color;
  border-bottom: 1rpx solid $border-color;
  box-shadow: $shadow-light;
}

.navbar-content {
  height: $navbar-height;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $spacing-xl;
}

.navbar-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-sm;
  @include button-hover;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .nav-button {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: $border-radius-sm;
    @include button-hover;
  }
}

.history-icon, .new-chat-icon {
  font-size: 32rpx;
  color: $text-primary;
}

.navbar-center {
  flex: 1;
  text-align: center;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 500;
  color: $text-primary;
}
</style>
