<script setup lang="ts">
interface Question {
  id: string
  faq: string
  knowledgeName: string
}

interface Props {
  questions: Question[]
  loading?: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  selectQuestion: [question: string]
}>()

const handleSelectQuestion = (question: Question) => {
  emit('selectQuestion', question.faq)
}
</script>

<template>
  <view class="common-questions">
    <view class="questions-header">
      <text class="questions-title">💡 常用问题</text>
    </view>
    
    <view v-if="loading" class="loading-state">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <view v-else-if="questions.length > 0" class="questions-list">
      <view
        v-for="question in questions"
        :key="question.id"
        class="question-item"
        @click="handleSelectQuestion(question)"
      >
        <text class="question-text">{{ question.faq }}</text>
        <text class="question-arrow">→</text>
      </view>
    </view>
    
    <view v-else class="empty-state">
      <text class="empty-text">暂无常用问题</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/variables.scss';

.common-questions {
  background-color: $sidebar-bg-color;
  border-radius: $border-radius-sm;
  margin: $spacing-md;
  overflow: hidden;
  box-shadow: $shadow-light;
}

.questions-header {
  padding: $spacing-lg $spacing-xl;
  border-bottom: 1rpx solid $border-color;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
}

.questions-title {
  font-size: 30rpx;
  font-weight: 600;
  color: $text-primary;
}

.loading-state {
  padding: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-md;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #F3F3F3;
  border-top: 3rpx solid $accent-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 26rpx;
  color: $text-secondary;
}

.questions-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.question-item {
  padding: $spacing-lg $spacing-xl;
  border-bottom: 1rpx solid #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all $transition-fast;

  &:active {
    background-color: #F8FAFC;
  }

  &:last-child {
    border-bottom: none;
  }
}

.question-text {
  flex: 1;
  font-size: 28rpx;
  color: $text-primary;
  line-height: 1.5;
  margin-right: $spacing-md;
}

.question-arrow {
  font-size: 24rpx;
  color: $text-secondary;
  font-weight: bold;
}

.empty-state {
  padding: 60rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: $text-secondary;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
