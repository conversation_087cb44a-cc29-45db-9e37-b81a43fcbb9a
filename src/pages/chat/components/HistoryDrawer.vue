<script setup>
import { ref, watch, nextTick } from 'vue'
import { useUserStore } from '@/store/user'
import { getStatusBarHeight } from '@/utils/tools'

const props = defineProps({
  show: <PERSON><PERSON><PERSON>,
  historyList: Array
})

const emit = defineEmits(['close', 'selectHistory'])

const userStore = useUserStore()
const statusBarHeight = getStatusBarHeight()

// 控制DOM渲染
const isRendered = ref(false)
// 控制抽屉位置
const drawerTranslateX = ref(-300)
// 控制拖拽状态
const isDragging = ref(false)
// 触摸滑动相关
const touchStartX = ref(0)
const touchStartY = ref(0)

// 监听父组件的show属性，管理动画和渲染
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      // 1. 渲染组件
      isRendered.value = true
      // 2. 等待DOM更新后，执行进入动画
      nextTick(() => {
        drawerTranslateX.value = 0
      })
    } else {
      // 1. 执行退出动画
      drawerTranslateX.value = -300
      // 2. 动画结束后，从DOM中移除
      setTimeout(() => {
        isRendered.value = false
      }, 300) // 动画时长
    }
  }
)

const handleClose = () => {
  emit('close')
}

const handleSelectHistory = (history) => {
  emit('selectHistory', history)
}

// 触摸开始
const handleTouchStart = (e) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
  isDragging.value = true
}

// 触摸移动
const handleTouchMove = (e) => {
  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY
  const deltaX = currentX - touchStartX.value
  const deltaY = Math.abs(currentY - touchStartY.value)

  if (Math.abs(deltaX) > deltaY && deltaX < 0) {
    e.preventDefault()
    drawerTranslateX.value = Math.max(deltaX, -300)
  }
}

// 触摸结束
const handleTouchEnd = () => {
  isDragging.value = false
  if (drawerTranslateX.value < -100) {
    handleClose()
  } else {
    // 弹回原位
    drawerTranslateX.value = 0
  }
}

// 退出登录
const handleLogout = () => {
  handleClose()
  setTimeout(() => {
    uni.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          userStore.logout()
        }
      }
    })
  }, 300)
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}
</script>

<template>
  <view
    v-if="isRendered"
    class="history-drawer"
    :class="{ 'is-closing': !show }"
    @click="handleClose"
  >
    <view
      class="drawer-content"
      :class="{ 'is-dragging': isDragging }"
      :style="{
        transform: `translateX(${drawerTranslateX}px)`
      }"
      @click.stop
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <view class="drawer-header" :style="{ paddingTop: statusBarHeight + 'px' }">
        <text class="drawer-title">历史对话</text>
      </view>
      <scroll-view class="history-list" scroll-y>
        <view
          v-for="history in historyList"
          :key="history.id"
          class="history-item"
          @click="handleSelectHistory(history)"
        >
          <view class="history-content">
            <text class="history-title">{{ history.query || '对话记录' }}</text>
            <text class="history-time">{{ formatTime(history.createTime) }}</text>
          </view>
          <view class="history-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
        <view v-if="historyList.length === 0" class="empty-history">
          <text class="empty-text">暂无历史对话</text>
        </view>
      </scroll-view>
      <view class="user-info-section">
        <view class="user-info">
          <view class="user-avatar">
            <text class="avatar-text">{{ (userStore.userInfo.userName || '用户').charAt(0).toUpperCase() }}</text>
          </view>
          <view class="user-details">
            <text class="user-name">{{ userStore.userInfo.userName || '用户' }}</text>
            <text class="user-role">{{ userStore.userInfo.nickName || '普通用户' }}</text>
          </view>
        </view>
        <view class="logout-btn" @click="handleLogout">
          <text class="logout-text">退出</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.history-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.3s ease;
  z-index: 999;
}

.history-drawer:not(.is-closing) {
  background-color: rgba(0, 0, 0, 0.4);
}

.drawer-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 80%;
  top: 0;
  bottom: 0;
  max-width: 300px;
  background-color: #f7f8fb;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  transform: translateX(-300px);
}

.drawer-content.is-dragging {
  transition: none;
}

.drawer-header {
  @include header-style;
  background: linear-gradient(135deg, $header-bg-color 0%, rgba($accent-color, 0.05) 100%);
  justify-content: center;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $text-primary;
}

.history-list {
  flex: 1;
  min-height: 0;
  padding: $spacing-md 0;
  overflow-y: auto;
}

.history-item {
  padding: $spacing-lg $spacing-xl;
  margin: 0 $spacing-md $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba($border-color, 0.5);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.history-item:active {
  background-color: rgba($accent-color, 0.1);
  border-color: rgba($accent-color, 0.3);
  transform: translateX(8rpx);
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  flex: 1;
  min-width: 0;
}

.history-title {
  font-size: 28rpx;
  color: $text-primary;
  line-height: 1.4;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.history-time {
  font-size: 24rpx;
  color: $text-secondary;
}

.history-arrow {
  opacity: 0.6;
}

.arrow-icon {
  font-size: 32rpx;
  color: $text-secondary;
}

.empty-history {
  height: 200rpx;
  @include empty-state;
  margin: $spacing-xl;
  border-radius: $border-radius-md;
  background-color: rgba(255, 255, 255, 0.5);
}

.empty-text {
  font-size: 28rpx;
  color: $text-secondary;
}

.user-info-section {
  border-top: 1rpx solid rgba($border-color, 0.3);
  padding: $spacing-lg $spacing-xl;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, $accent-color 0%, lighten($accent-color, 10%) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 600;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: $text-primary;
}

.user-role {
  font-size: 24rpx;
  color: $text-secondary;
}

.logout-btn {
  padding: 16rpx 24rpx;
  background-color: rgba($accent-color, 0.1);
  border-radius: $border-radius-md;
  transition: background-color 0.2s ease;
}

.logout-btn:active {
  background-color: rgba($accent-color, 0.2);
}

.logout-text {
  font-size: 26rpx;
  color: $accent-color;
  font-weight: 600;
}
</style>
