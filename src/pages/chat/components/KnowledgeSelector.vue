<script setup lang="ts">
interface Knowledge {
  label: string
  value: string
}

interface Props {
  show: boolean
  knowledgeList: Knowledge[]
  currentKnowledge?: Knowledge
}

defineProps<Props>()

const emit = defineEmits<{
  close: []
  selectKnowledge: [knowledge: Knowledge]
}>()

const handleClose = () => {
  emit('close')
}

const handleSelectKnowledge = (knowledge: Knowledge) => {
  emit('selectKnowledge', knowledge)
}
</script>

<template>
  <view v-if="show" class="knowledge-selector" @click="handleClose">
    <view class="selector-content" @click.stop>
      <view class="selector-header">
        <text class="selector-title">选择知识库</text>
        <text class="close-btn" @click="handleClose">×</text>
      </view>
      <scroll-view class="knowledge-list" scroll-y>
        <view
          v-for="knowledge in knowledgeList"
          :key="knowledge.label"
          class="knowledge-item"
          :class="{ 'active': currentKnowledge?.label === knowledge.label }"
          @click="handleSelectKnowledge(knowledge)"
        >
          <text class="knowledge-name">{{ knowledge.label }}</text>
          <text v-if="currentKnowledge?.label === knowledge.label" class="check-icon">✓</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.knowledge-selector {
  @include modal-overlay;
}

.selector-content {
  @include modal-content;
}

.selector-header {
  @include header-style;
}

.selector-title {
  font-size: 32rpx;
  font-weight: 500;
  color: $text-primary;
}

.close-btn {
  font-size: 48rpx;
  color: $text-secondary;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $border-radius-circle;
  @include button-hover;
}

.knowledge-list {
  max-height: 600rpx;
}

.knowledge-item {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  @include list-item;

  &.active {
    background-color: #F0F8FF;
    border-left: 4rpx solid $accent-color;
  }
}

.knowledge-name {
  font-size: 30rpx;
  color: $text-primary;
  flex: 1;
}

.check-icon {
  font-size: 32rpx;
  color: $accent-color;
  font-weight: bold;
}
</style>
