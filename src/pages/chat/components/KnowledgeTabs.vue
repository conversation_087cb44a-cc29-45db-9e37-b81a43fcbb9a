<script setup lang="ts">
interface Knowledge {
  label: string
  value: string
}

interface Props {
  knowledgeList: Knowledge[]
  currentKnowledge?: Knowledge
  safeAreaBottom?: number
}

const props = withDefaults(defineProps<Props>(), {
  safeAreaBottom: 0
})

const emit = defineEmits<{
  selectKnowledge: [knowledge: Knowledge]
}>()

const handleSelectKnowledge = (knowledge: Knowledge) => {
  emit('selectKnowledge', knowledge)
}

// 计算底部位置：输入框基础高度 + 安全区域 + 间距
const bottomPosition = 120 + (props.safeAreaBottom || 20) + 20
</script>

<template>
  <view class="knowledge-tabs" :style="{ bottom: bottomPosition + 'rpx' }">
    <scroll-view
      class="tabs-scroll"
      scroll-x
      :show-scrollbar="false"
      :enable-flex="true"
    >
      <view class="tabs-container">
        <view
          v-for="knowledge in knowledgeList"
          :key="knowledge.label"
          class="tab-item"
          :class="{ 'active': currentKnowledge?.label === knowledge.label }"
          @click="handleSelectKnowledge(knowledge)"
        >
          <text class="tab-text">{{ knowledge.label }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/variables.scss';

.knowledge-tabs {
  position: fixed;
  left: 0;
  right: 0;
  background-color: $input-area-bg;
  border-top: 1rpx solid $border-color;
  padding: $spacing-md 0;
  z-index: 99;
  box-shadow: 0 -1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.tabs-scroll {
  width: 100%;
  white-space: nowrap;
}

.tabs-container {
  display: flex;
  align-items: center;
  padding: 0 $spacing-md;
  gap: $spacing-md;
}

.tab-item {
  flex-shrink: 0;
  padding: $spacing-sm $spacing-lg;
  background-color: #F8F9FA;
  border: 1rpx solid $border-color;
  border-radius: 20rpx;
  transition: all $transition-fast;
  min-width: 120rpx;
  text-align: center;

  &:active {
    transform: scale(0.95);
  }

  &.active {
    background-color: $accent-color;
    border-color: $accent-color;
    
    .tab-text {
      color: #FFFFFF;
      font-weight: 500;
    }
  }
}

.tab-text {
  font-size: 28rpx;
  color: $text-primary;
  white-space: nowrap;
  line-height: 1.2;
}
</style>
