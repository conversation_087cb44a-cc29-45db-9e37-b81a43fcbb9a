<script setup lang="ts">
interface Props {
  show: boolean
  text?: string
}

withDefaults(defineProps<Props>(), {
  text: '初始化中...'
})
</script>

<template>
  <view v-if="show" class="loading-mask">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ text }}</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.loading-mask {
  @include modal-overlay;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-xl;
  padding: 60rpx;
  background-color: $sidebar-bg-color;
  border-radius: $border-radius-sm;
  box-shadow: $shadow-medium;
}

.loading-spinner {
  @include loading-spinner;
}

.loading-text {
  font-size: 28rpx;
  color: $text-primary;
}
</style>
