<script setup lang="ts">
import { computed, nextTick, onMounted, watch, ref } from 'vue'

interface Props {
  content: string
  isStreaming?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isStreaming: false
})

// 思考内容折叠状态 - 默认展开
const activeThinkPanel = ref(['think'])

// 解析思考内容
const thinkContent = computed(() => {
  if (!props.content) return ''

  const text = props.content

  // 提取完整的think标签内容
  const thinkMatch = text.match(/<think>([\s\S]*?)<\/think>/)
  if (thinkMatch) {
    return thinkMatch[1].trim()
  }

  // 如果有未闭合的think标签（流式输出中）
  if (text.includes('<think>')) {
    const openThinkMatch = text.match(/<think>([\s\S]*)$/)
    if (openThinkMatch) {
      return openThinkMatch[1].trim()
    }
  }

  return ''
})

// 获取普通内容（移除think标签）
const normalContent = computed(() => {
  if (!props.content) return ''

  let content = props.content

  // 移除完整的think标签
  content = content.replace(/<think>[\s\S]*?<\/think>/g, '')

  // 如果消息未完成，移除未闭合的think标签
  if (props.isStreaming) {
    content = content.replace(/<think>[\s\S]*$/, '')
  }

  return content.trim()
})

// 触发 Mermaid 渲染 - 只在流式输出完成后渲染
const triggerMermaidRender = () => {
  // 只在非流式状态下渲染 mermaid，避免未闭合代码块导致的渲染错误
  if (props.isStreaming) return

  nextTick(() => {
    try {
      // v-md-editor 会自动处理 mermaid 渲染
      // 这里主要是为了确保在流式输出完成后触发渲染
      const mermaidElements = document.querySelectorAll('.mermaid:not([data-processed])')

      if (mermaidElements.length > 0) {
        // 标记为已处理，避免重复渲染
        mermaidElements.forEach((element) => {
          element.setAttribute('data-processed', 'true')
        })
      }
    } catch (error) {
      console.error('Error in triggerMermaidRender:', error)
    }
  })
}

// 监听流式状态变化，只在完成时触发 Mermaid 渲染
watch(() => props.isStreaming, (newVal) => {
  if (!newVal) {
    // 流式输出完成，延迟一点时间确保内容已更新
    setTimeout(() => {
      triggerMermaidRender()
    }, 100)
  }
}, { immediate: false })

// 监听内容变化，但只在非流式状态下渲染
watch(() => props.content, () => {
  if (!props.isStreaming) {
    triggerMermaidRender()
  }
}, { immediate: false })

onMounted(() => {
  if (!props.isStreaming) {
    triggerMermaidRender()
  }
})
</script>

<template>
  <view class="markdown-renderer">
    <!-- 思考内容 -->
    <view v-if="thinkContent" class="think-section">
      <view class="think-collapse">
        <view
          class="think-header"
          @click="activeThinkPanel = activeThinkPanel.length > 0 ? [] : ['think']"
        >
          <text class="think-icon">🤔</text>
          <text class="think-title">AI 思考过程</text>
          <text class="think-arrow" :class="{ 'expanded': activeThinkPanel.length > 0 }">▼</text>
        </view>
        <view v-if="activeThinkPanel.length > 0" class="think-content">
          <text class="think-text">{{ thinkContent }}</text>
          <!-- 思考过程中的流式指示器 -->
          <view v-if="isStreaming && thinkContent && props.content.includes('<think>') && !props.content.includes('</think>')" class="think-streaming-indicator">
            <text class="cursor-blink">|</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 正常内容 -->
    <v-md-preview
      v-if="normalContent"
      :text="normalContent"
      class="markdown-content"
    />

    <!-- 流式输入指示器 -->
    <view v-if="isStreaming && normalContent" class="streaming-indicator">
      <text class="cursor-blink">|</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.markdown-renderer {
  width: 100%;

  // Think 部分样式
  .think-section {
    margin: 0 0 32rpx 0;

    .think-collapse {
      border: 2rpx solid #e1e5e9;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .think-header {
      background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
      border-bottom: 2rpx solid #e1e5e9;
      padding: 24rpx 32rpx;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: background 0.3s ease;

      &:active {
        background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
      }

      .think-icon {
        margin-right: 20rpx;
        font-size: 32rpx;
        filter: grayscale(0.3);
      }

      .think-title {
        flex: 1;
        font-size: 28rpx;
        font-weight: 600;
        color: #5f6368;
      }

      .think-arrow {
        font-size: 24rpx;
        color: #5f6368;
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }

    .think-content {
      padding: 32rpx;
      background: #ffffff;

      .think-text {
        color: #8b8b8b;
        font-size: 26rpx;
        line-height: 1.7;
        white-space: pre-wrap;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .think-streaming-indicator {
        display: inline-block;
        margin-left: 8rpx;

        .cursor-blink {
          font-size: 26rpx;
          color: #8b8b8b;
          animation: blink 1s infinite;
        }
      }
    }
  }

  .markdown-content {
    // 重置默认样式
    :deep(.github-markdown-body) {
      padding: 0 !important;
      background: transparent !important;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      font-size: 28rpx;
      line-height: 1.6;
      color: #333;
      
      // 标题样式
      h1, h2, h3, h4, h5, h6 {
        margin: 24rpx 0 16rpx 0;
        font-weight: 600;
        line-height: 1.4;
      }
      
      h1 { font-size: 36rpx; }
      h2 { font-size: 32rpx; }
      h3 { font-size: 30rpx; }
      h4 { font-size: 28rpx; }
      h5 { font-size: 26rpx; }
      h6 { font-size: 24rpx; }
      
      // 段落样式
      p {
        margin: 16rpx 0;
        line-height: 1.7;
      }
      
      // 代码样式
      code {
        background: #f5f5f5;
        padding: 4rpx 8rpx;
        border-radius: 6rpx;
        font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
        font-size: 24rpx;
        color: #e83e8c;
      }
      
      pre {
        background: #f8f9fa;
        border: 1rpx solid #e9ecef;
        border-radius: 12rpx;
        padding: 24rpx;
        overflow-x: auto;
        margin: 24rpx 0;
        
        code {
          background: none;
          padding: 0;
          color: inherit;
          font-size: 24rpx;
        }
      }
      
      // 列表样式
      ul, ol {
        margin: 16rpx 0;
        padding-left: 40rpx;
        
        li {
          margin: 8rpx 0;
          line-height: 1.6;
        }
      }
      
      // 引用样式
      blockquote {
        border-left: 8rpx solid #007aff;
        margin: 24rpx 0;
        padding: 16rpx 24rpx;
        background: #f8f9fa;
        color: #666;
        border-radius: 0 8rpx 8rpx 0;
        
        p {
          margin: 0;
        }
      }
      
      // 表格样式
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 24rpx 0;
        font-size: 26rpx;
        
        th, td {
          border: 1rpx solid #e9ecef;
          padding: 16rpx 20rpx;
          text-align: left;
        }
        
        th {
          background: #f8f9fa;
          font-weight: 600;
        }
        
        tr:nth-child(even) {
          background: #f8f9fa;
        }
      }
      
      // 链接样式
      a {
        color: #007aff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
      
      // 分割线样式
      hr {
        border: none;
        height: 1rpx;
        background: #e9ecef;
        margin: 32rpx 0;
      }
      
      // 图片样式
      img {
        max-width: 100%;
        height: auto;
        border-radius: 8rpx;
        margin: 16rpx 0;
      }

      // Mermaid 图表样式
      .mermaid {
        text-align: center;
        margin: 32rpx 0;
        padding: 32rpx;
        background: #f9f9f9;
        border-radius: 16rpx;
        border: 2rpx solid #e1e4e8;
        overflow-x: auto;

        svg {
          max-width: 100%;
          height: auto;
        }
      }

      // Mermaid 错误样式
      .mermaid-error {
        color: #dc3545;
        background: #f8d7da;
        border: 2rpx solid #f5c6cb;
        border-radius: 8rpx;
        padding: 24rpx;
        font-size: 26rpx;
        text-align: center;
      }
    }
  }
  

  
  .streaming-indicator {
    display: inline-block;
    margin-left: 8rpx;
    
    .cursor-blink {
      font-size: 28rpx;
      color: #007aff;
      animation: blink 1s infinite;
    }
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}
</style>
