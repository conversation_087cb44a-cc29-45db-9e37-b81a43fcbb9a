<script setup lang="ts">
import MessageItem from './MessageItem.vue'
import CommonQuestions from './CommonQuestions.vue'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  isStreaming?: boolean
}

interface Question {
  id: string
  faq: string
  knowledgeName: string
}

interface Props {
  messages: Message[]
  scrollTop: number
  currentKnowledgeName?: string
  commonQuestions?: Question[]
  questionsLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  commonQuestions: () => [],
  questionsLoading: false
})

const emit = defineEmits<{
  selectQuestion: [question: string]
}>()

const handleSelectQuestion = (question: string) => {
  emit('selectQuestion', question)
}
</script>

<template>
  <scroll-view
    class="message-list"
    scroll-y
    :scroll-top="scrollTop"
    :scroll-with-animation="true"
  >
    <view class="message-wrapper">
      <!-- 空状态 - 显示常用问题 -->
      <template v-if="messages.length === 0">
        <view class="welcome-section">
          <view class="welcome-message">
            <view class="welcome-icon">🤖</view>
            <text class="welcome-title">欢迎使用智能助手</text>
            <text class="welcome-desc">选择下方问题快速开始，或直接输入您的问题</text>
          </view>
        </view>

        <!-- 常用问题 -->
        <CommonQuestions
          :questions="commonQuestions"
          :loading="questionsLoading"
          @select-question="handleSelectQuestion"
        />
      </template>

      <!-- 消息列表 -->
      <MessageItem
        v-for="message in messages"
        :key="message.id"
        :message="message"
        :current-knowledge-name="currentKnowledgeName"
      />
    </view>
  </scroll-view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.message-list {
  flex: 1;
  padding: $spacing-md;
  padding-bottom: 300rpx; /* 为固定的底部面板留出空间 */
  box-sizing: border-box;
}

.message-wrapper {
  min-height: 100%;
}

.welcome-section {
  padding: 60rpx $spacing-md 40rpx;
}

.welcome-message {
  text-align: center;
  margin-bottom: $spacing-xl;
}

.welcome-icon {
  font-size: 80rpx;
  margin-bottom: $spacing-lg;
}

.welcome-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: $spacing-sm;
}

.welcome-desc {
  display: block;
  font-size: 28rpx;
  color: $text-secondary;
  line-height: 1.5;
}
</style>
