<template>
  <view class="chart-container">
    <div
      ref="chartRef"
      class="chart-canvas"
      :style="{ width: width, height: height }"
    ></div>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

interface ChartData {
  label: string
  value: number
}

interface Props {
  data: ChartData[]
  title?: string
  type: 'pie' | 'bar' | 'line' | 'table'
  width?: string
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  width: '100%',
  height: '300px'
})

const emit = defineEmits<{
  click: [data: any]
}>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 根据图表类型生成配置
const chartOption = computed(() => {
  if (!props.data || props.data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  // 处理长标题换行
  const formatTitle = (title: string) => {
    if (!title) return ''
    // 如果标题长度超过20个字符，在合适的位置插入换行符
    if (title.length > 20) {
      // 寻找合适的断点（逗号、句号、空格等）
      const breakPoints = [',', '，', '。', ' ', '的', '和', '与']
      let bestBreakPoint = Math.floor(title.length / 2)

      for (let i = 10; i < title.length - 10; i++) {
        if (breakPoints.includes(title[i])) {
          bestBreakPoint = i + 1
          break
        }
      }

      return title.substring(0, bestBreakPoint) + '\n' + title.substring(bestBreakPoint)
    }
    return title
  }

  const baseOption = {
    title: {
      text: formatTitle(props.title),
      left: 'center',
      top: '2%', // 标题位置适中
      textStyle: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#333',
        lineHeight: 16 // 设置行高
      }
    },
    tooltip: {
      trigger: 'item',
      confine: true
    },
    animation: true,
    animationDuration: 1000
  }

  switch (props.type) {
    case 'pie':
      return {
        ...baseOption,
        tooltip: {
          ...baseOption.tooltip,
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [{
          name: props.title || '数据统计',
          type: 'pie',
          radius: '45%', // 稍微减小半径给标签留空间
          center: ['50%', '58%'], // 减少标题和饼图间距
          data: props.data.map((item, index) => {
            // 定义饼图颜色列表
            const colorList = [
              '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE',
              '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'
            ];
            return {
              name: item.label,
              value: item.value,
              itemStyle: {
                color: colorList[index % colorList.length]
              }
            }
          }),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            fontSize: 12
          }
        }]
      }

    case 'bar':
      return {
        ...baseOption,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '22%', // 减少标题和图表间距
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: props.data.map(item => item.label),
          axisLabel: {
            fontSize: 12,
            interval: 0,
            rotate: props.data.length > 5 ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12
          }
        },
        series: [{
          name: props.title || '数值',
          type: 'bar',
          data: props.data.map(item => item.value),
          itemStyle: {
            color: (params: any) => {
              // 定义一个颜色列表，与PC端保持一致
              const colorList = [
                '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE',
                '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'
              ];
              // 通过取余操作循环使用颜色
              return colorList[params.dataIndex % colorList.length];
            }
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 12
          }
        }]
      }

    case 'line':
      return {
        ...baseOption,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '22%', // 减少标题和图表间距
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: props.data.map(item => item.label),
          axisLabel: {
            fontSize: 12,
            interval: 0,
            rotate: props.data.length > 5 ? 45 : 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            fontSize: 12
          }
        },
        series: [{
          name: props.title || '数值',
          type: 'line',
          data: props.data.map(item => item.value),
          smooth: true,
          itemStyle: {
            color: '#5470C6'
          },
          lineStyle: {
            color: '#5470C6'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(84, 112, 198, 0.3)'
              }, {
                offset: 1, color: 'rgba(84, 112, 198, 0.1)'
              }]
            }
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 12
          }
        }]
      }

    default:
      return baseOption
  }
})

// 初始化图表
const initChart = async () => {
  console.log('initChart 开始', chartRef.value)
  if (!chartRef.value) {
    console.log('chartRef.value 不存在')
    return
  }

  await nextTick()

  // 添加延迟确保DOM完全渲染
  setTimeout(async () => {
    try {
      // 销毁已存在的实例
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }

      console.log('开始创建 ECharts 实例', chartRef.value)

      // 确保div元素有尺寸
      const container = chartRef.value
      console.log('容器信息:', {
        offsetWidth: container.offsetWidth,
        offsetHeight: container.offsetHeight,
        clientWidth: container.clientWidth,
        clientHeight: container.clientHeight
      })

      // 强制设置容器尺寸
      container.style.width = '100%'
      container.style.height = '300px'

      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.log('容器尺寸为0，等待DOM渲染')
        // 强制重新计算尺寸
        await nextTick()
        // 再次检查尺寸
        console.log('重新检查容器尺寸:', {
          offsetWidth: container.offsetWidth,
          offsetHeight: container.offsetHeight
        })
      }

      // 创建新实例
      chartInstance = echarts.init(container)
      console.log('ECharts 实例创建成功', chartInstance)

      // 设置配置
      console.log('设置图表配置', chartOption.value)
      chartInstance.setOption(chartOption.value)

      // 强制调整图表尺寸
      setTimeout(() => {
        if (chartInstance) {
          chartInstance.resize()
          console.log('图表尺寸已调整')
        }
      }, 50)

      // 绑定点击事件
      chartInstance.on('click', (params: any) => {
        emit('click', params)
      })

      // 响应式调整
      if (typeof ResizeObserver !== 'undefined') {
        const resizeObserver = new ResizeObserver(() => {
          if (chartInstance) {
            chartInstance.resize()
          }
        })
        resizeObserver.observe(canvas)
      }

      console.log('图表初始化完成')
    } catch (error) {
      console.error('图表初始化失败:', error)
    }
  }, 100)
}

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(chartOption.value, true)
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.type, () => {
  updateChart()
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  display: block;
  padding: 16rpx 24rpx; // 给图表容器添加内边距
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-canvas {
  width: 100% !important;
  height: 380px !important;
  min-height: 380px;
  display: block;
}
</style>
