<template>
  <view v-if="processedChart" class="chart-container">
    <!-- 图表标签 -->
    <view class="chart-tag">
      <text class="tag-text">📊 图表数据</text>
    </view>
    
    <view class="chart-content">
      <!-- 图表类型：饼图、柱状图、折线图 -->
      <BaseChart
        v-if="isChartType"
        :data="processedChart.data"
        :title="processedChart.title"
        :type="processedChart.type"
        :width="chartWidth"
        :height="chartHeight"
        @click="handleChartClick"
      />
      
      <!-- 表格类型 -->
      <DataTable
        v-if="processedChart.type === 'table'"
        :data="processedChart.data"
        :title="processedChart.title"
        :column-labels="processedChart.columnLabels"
        :max-display-rows="10"
      />
      
      <!-- 同时显示图表和表格 -->
      <view v-if="shouldShowTable && isChartType" class="additional-table">
        <view class="table-divider">
          <text class="divider-text">数据详情</text>
        </view>
        <DataTable
          :data="processedChart.data"
          :column-labels="processedChart.columnLabels"
          :max-display-rows="5"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'
import DataTable from './DataTable.vue'

interface Props {
  chartData: string | null
  width?: string
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '300px'
})

const emit = defineEmits<{
  chartClick: [data: any]
}>()

// 解析图表数据
const processedChart = computed(() => {
  console.log('ChartContainer 接收到的数据:', props.chartData)
  if (!props.chartData) {
    console.log('没有图表数据')
    return null
  }

  try {
    const parsed = JSON.parse(props.chartData)
    console.log('解析后的图表数据:', parsed)

    // 验证数据格式
    if (!parsed.type || !parsed.data) {
      console.warn('图表数据格式不正确:', parsed)
      return null
    }

    console.log('图表数据验证通过')
    return parsed
  } catch (error) {
    console.error('解析图表数据失败:', error, '原始数据:', props.chartData)
    return null
  }
})

// 是否是图表类型（非表格）
const isChartType = computed(() => {
  return processedChart.value && 
         ['pie', 'bar', 'line'].includes(processedChart.value.type)
})

// 是否需要显示表格
const shouldShowTable = computed(() => {
  return processedChart.value && 
         processedChart.value.showTable === true &&
         isChartType.value
})

// 图表尺寸
const chartWidth = computed(() => props.width)
const chartHeight = computed(() => props.height)

// 处理图表点击事件
const handleChartClick = (data: any) => {
  emit('chartClick', data)
}
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  margin: 16rpx 0; // 减少上下边距
}

.chart-tag {
  margin-bottom: 16rpx; // 减少底部边距

  .tag-text {
    display: inline-block;
    padding: 6rpx 12rpx; // 减少内边距
    background-color: rgba(16, 185, 129, 0.1);
    color: #10B981;
    font-size: 22rpx; // 稍微减小字体
    font-weight: 600;
    border-radius: 6rpx;
    border: 1rpx solid rgba(16, 185, 129, 0.2);
  }
}

.chart-content {
  background-color: transparent; // 移除灰色背景
  border: none; // 移除边框
  border-radius: 8rpx;
  padding: 16rpx 0; // 只保留上下内边距，左右不要
  overflow: hidden;
}

.additional-table {
  margin-top: 48rpx;
}

.table-divider {
  padding: 24rpx 0;
  text-align: center;
  border-bottom: 1rpx solid #e4e7ed;
  margin-bottom: 24rpx;
  
  .divider-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #666;
  }
}
</style>
