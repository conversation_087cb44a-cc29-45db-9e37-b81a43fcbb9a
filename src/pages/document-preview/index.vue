<template>
  <view class="preview-page">
    <!-- 文档预览区域 -->
    <view class="preview-container">
      <DocumentPreview
        :file-url="fileUrl"
        :file-name="fileName"
        :file-type="fileType"
      />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import DocumentPreview from '@/components/DocumentPreview/index.vue'

const fileUrl = ref('')
const fileName = ref('')
const fileType = ref('')

// 使用onLoad获取页面参数
const onLoad = (options) => {
  fileUrl.value = decodeURIComponent(options.fileUrl || '')
  fileName.value = decodeURIComponent(options.fileName || '')
  fileType.value = options.fileType || ''

  console.log('页面参数:', { fileUrl: fileUrl.value, fileName: fileName.value, fileType: fileType.value })
}
</script>

<style scoped lang="scss">
.preview-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.preview-container {
  flex: 1;
  background-color: #f5f5f5;
}
</style>
