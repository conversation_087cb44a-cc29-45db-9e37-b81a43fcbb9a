<template>
  <view class="test-office-page">
    <view class="header">
      <text class="title">Vue Office 测试页面</text>
    </view>
    
    <view class="content">
      <view class="test-section">
        <text class="section-title">PDF预览测试</text>
        <view class="preview-container">
          <VueOfficePdf 
            src="https://morth.nic.in/sites/default/files/dd12-13_0.pdf"
            style="height: 300px; width: 100%;"
            @rendered="handlePdfRendered"
            @error="handlePdfError"
          />
        </view>
      </view>
      
      <view class="test-section">
        <text class="section-title">Word文档预览测试</text>
        <view class="preview-container">
          <VueOfficeDocx 
            src="https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/demo.docx"
            style="height: 300px; width: 100%;"
            @rendered="handleDocxRendered"
            @error="handleDocxError"
          />
        </view>
      </view>
      
      <view class="test-section">
        <text class="section-title">Excel文档预览测试</text>
        <view class="preview-container">
          <VueOfficeExcel 
            src="https://file-examples.com/storage/fe68c8a7c66b2b9c7e0b3e8/2017/10/file_example_XLSX_10.xlsx"
            style="height: 300px; width: 100%;"
            @rendered="handleExcelRendered"
            @error="handleExcelError"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const handlePdfRendered = () => {
  console.log('PDF渲染完成')
  uni.showToast({
    title: 'PDF加载成功',
    icon: 'success'
  })
}

const handlePdfError = (err: any) => {
  console.error('PDF加载错误:', err)
  uni.showToast({
    title: 'PDF加载失败',
    icon: 'error'
  })
}

const handleDocxRendered = () => {
  console.log('Word文档渲染完成')
  uni.showToast({
    title: 'Word加载成功',
    icon: 'success'
  })
}

const handleDocxError = (err: any) => {
  console.error('Word加载错误:', err)
  uni.showToast({
    title: 'Word加载失败',
    icon: 'error'
  })
}

const handleExcelRendered = () => {
  console.log('Excel文档渲染完成')
  uni.showToast({
    title: 'Excel加载成功',
    icon: 'success'
  })
}

const handleExcelError = (err: any) => {
  console.error('Excel加载错误:', err)
  uni.showToast({
    title: 'Excel加载失败',
    icon: 'error'
  })
}
</script>

<style scoped lang="scss">
.test-office-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.test-section {
  background-color: white;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.preview-container {
  border: 1rpx solid #e4e7ed;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #fafafa;
}
</style>
