import { defineStore } from 'pinia'
import { login, getUserInfo, logout } from '@/api/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 只从缓存获取 token
    token: uni.getStorageSync('token') || '',
    // 用户信息全部在 Store 中管理
    userInfo: null,
    models: [],
    permissions: [],
    roles: []
  }),

  getters: {
    // 是否已登录
    isLoggedIn: (state) => !!state.token,
    // 是否已获取用户信息
    hasUserInfo: (state) => !!state.userInfo,
    // 用户名
    username: (state) => state.userInfo?.userName || '',
    // 用户昵称
    nickname: (state) => state.userInfo?.nickName || ''
  },

  actions: {
    /**
     * 登录
     */
    async login(loginData) {
      try {
        const response = await login(
          loginData.username,
          loginData.password,
          loginData.code,
          loginData.uuid
        )

        if (response.code === 200) {
          // 只缓存 token
          this.token = response.data.access_token
          uni.setStorageSync('token', this.token)

          // 登录成功后获取用户信息
          await this.fetchUserInfo()

          return { success: true, data: response.data }
        } else {
          throw new Error(response.msg || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },

    /**
     * 获取用户信息
     */
    async fetchUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 200) {
          // 存储用户信息到 Store
          this.userInfo = response.user
          this.models = response.models || []
          this.permissions = response.permissions || []
          this.roles = response.roles || []
          
          return response
        } else {
          throw new Error(response.msg || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    },

    /**
     * 退出登录
     */
    async logout() {
      try {
        await logout()
      } catch (error) {
        console.error('退出登录接口调用失败:', error)
      } finally {
        this.clearUserData()
        this.redirectToLogin()
      }
    },

    /**
     * 清理用户数据
     */
    clearUserData() {
      this.token = ''
      this.userInfo = null
      this.models = []
      this.permissions = []
      this.roles = []
      uni.removeStorageSync('token')
    },

    /**
     * 跳转到登录页
     */
    redirectToLogin() {
      uni.reLaunch({ url: '/pages/login/index' })
    }
  }
})
