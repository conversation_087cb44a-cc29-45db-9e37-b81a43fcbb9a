import CryptoJS from 'crypto-js'

/**
 * AES 加密密码
 * @param {string} password 明文密码
 * @returns {string} 加密后的密码
 */
export function encryptPassword(password) {
  if (!password) return ''
  
  const message = CryptoJS.enc.Utf8.parse(password)
  const key = CryptoJS.enc.Utf8.parse('Coalmine1234!@#$')
  
  const encrypted = CryptoJS.AES.encrypt(message, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  }).toString()
  
  return encrypted
}

/**
 * AES 解密密码
 * @param {string} encryptedPassword 加密的密码
 * @returns {string} 解密后的密码
 */
export function decryptPassword(encryptedPassword) {
  if (!encryptedPassword) return ''
  
  const key = CryptoJS.enc.Utf8.parse('Coalmine1234!@#$')
  
  const decrypted = CryptoJS.AES.decrypt(encryptedPassword, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  
  return CryptoJS.enc.Utf8.stringify(decrypted).toString()
}
