/**
 * 文档预览工具函数
 */

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = {
  // PDF文件
  pdf: ['pdf'],
  // Office文档
  office: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  // 图片文件
  image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
  // 文本文件
  text: ['txt', 'md', 'json', 'xml', 'csv', 'log']
}

// 获取文件扩展名
export const getFileExtension = (fileName) => {
  return fileName.split('.').pop()?.toLowerCase() || ''
}

// 判断文件类型
export const getFileType = (fileName) => {
  const ext = getFileExtension(fileName)
  
  for (const [type, extensions] of Object.entries(SUPPORTED_FILE_TYPES)) {
    if (extensions.includes(ext)) {
      return type
    }
  }
  
  return 'unknown'
}

// 判断是否支持预览
export const isSupportedFile = (fileName) => {
  const fileType = getFileType(fileName)
  return fileType !== 'unknown'
}

// 判断是否为Office文件
export const isOfficeFile = (fileName) => {
  const ext = getFileExtension(fileName)
  return SUPPORTED_FILE_TYPES.office.includes(ext)
}

// 判断是否为PDF文件
export const isPdfFile = (fileName) => {
  const ext = getFileExtension(fileName)
  return SUPPORTED_FILE_TYPES.pdf.includes(ext)
}

// 判断是否为图片文件
export const isImageFile = (fileName) => {
  const ext = getFileExtension(fileName)
  return SUPPORTED_FILE_TYPES.image.includes(ext)
}

// 打开文档预览页面
export const openDocumentPreview = (options) => {
  const { fileUrl, fileName = '', fileType = '' } = options
  
  if (!fileUrl) {
    uni.showToast({
      title: '文件地址无效',
      icon: 'error'
    })
    return
  }
  
  // 检查文件类型
  const detectedType = fileType || getFileType(fileName)
  
  if (!isSupportedFile(fileName) && !fileType) {
    uni.showModal({
      title: '提示',
      content: '暂不支持预览此类型文件，是否下载？',
      success: (res) => {
        if (res.confirm) {
          downloadFile(fileUrl, fileName)
        }
      }
    })
    return
  }
  
  // 跳转到预览页面
  uni.navigateTo({
    url: `/pages/document-preview/index?fileUrl=${encodeURIComponent(fileUrl)}&fileName=${encodeURIComponent(fileName)}&fileType=${detectedType}`
  })
}

// 下载文件
export const downloadFile = (fileUrl, fileName = '') => {
  if (!fileUrl) {
    uni.showToast({
      title: '文件地址无效',
      icon: 'error'
    })
    return
  }
  
  uni.showLoading({
    title: '下载中...'
  })
  
  uni.downloadFile({
    url: fileUrl,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
        
        // 如果是图片，保存到相册
        if (isImageFile(fileName)) {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '已保存到相册',
                icon: 'success'
              })
            },
            fail: () => {
              uni.showToast({
                title: '保存失败',
                icon: 'error'
              })
            }
          })
        }
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'error'
        })
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'error'
      })
    }
  })
}

// 获取文件图标
export const getFileIcon = (fileName) => {
  const fileType = getFileType(fileName)
  
  const iconMap = {
    pdf: '📄',
    office: '📊',
    image: '🖼️',
    text: '📝',
    unknown: '📎'
  }
  
  return iconMap[fileType] || iconMap.unknown
}

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
