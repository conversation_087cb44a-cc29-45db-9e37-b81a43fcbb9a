import HttpRequest from 'luch-request'

import config from '@/config'
import { useUserStore } from '@/store/user'

/**
 * HTTP 请求拦截器
 */

const http = new HttpRequest({
  timeout: 120 * 1000, // 请求超时时间，2分钟
})

// 检查并获取用户信息
const ensureUserInfo = async (userStore, requestUrl) => {
  // 跳过用户信息接口本身，避免死循环
  if (requestUrl === '/system/user/getInfo' || userStore.hasUserInfo) {
    return
  }

  try {
    await userStore.fetchUserInfo()
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw new Error('获取用户信息失败，请重新登录')
  }
}

http.interceptors.request.use(async (request) => {
  const userStore = useUserStore()
  request.baseURL = config.baseURL

  // 豁免不需要认证的接口
  if (request.custom?.auth === false) {
    return request
  }

  // 检查 token
  if (!userStore.token) {
    throw new Error('未登录，请先登录')
  }

  // 添加认证头
  request.header.Authorization = 'Bearer ' + userStore.token

  // 确保用户信息存在
  await ensureUserInfo(userStore, request.url)

  return request
})

http.interceptors.response.use((response) => {
  const apiRes = response.data
  // 如果响应数据错误有额外的逻辑，而不只是仅仅弹出错误，请将 needProcessError 设置为 ture
  if (response.config.custom.needProcessError) {
    return apiRes
  }

  if (response.config.custom.isBlob) {
    return apiRes
  }

  if (apiRes.code === 200) {
    return apiRes
  }

  if (apiRes.code === 401) {
    const userStore = useUserStore()
    userStore.logout() // 调用 store 的 action 来处理登出逻辑
    return Promise.reject(apiRes) // 返回 Promise.reject 更符合拦截器逻辑
  }

  const errMessage = apiRes.msg || '系统繁忙，请稍后重试'

  uni.showModal({
    content: errMessage,
    showCancel: false,
    title: '提示',
  })

  const err = new Error(errMessage)
  console.log('err', err)
  return Promise.reject(err)
}, (err) => {
  console.error('err', err)
  uni.showModal({
    content: '系统繁忙，请稍后重试',
    showCancel: false,
    title: '提示',
  })
  return Promise.reject(err)
})

const request = http.request.bind(http)
export default request
