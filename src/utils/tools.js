/**
 * 获取状态栏高度
 * H5环境下根据设备类型返回合适的默认值，避免重叠问题
 */
export function getStatusBarHeight() {
  const systemInfo = uni.getSystemInfoSync()

  // 根据平台设置状态栏高度
  let statusHeight = systemInfo.statusBarHeight || 0

  // H5 环境下设置默认状态栏高度
  // #ifdef H5
  if (statusHeight === 0) {
    // 检查是否在移动设备上
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    if (isMobile) {
      // 移动设备默认状态栏高度
      statusHeight = 44 // iOS 默认
      if (/Android/i.test(navigator.userAgent)) {
        statusHeight = 48 // Android 默认
      }
    }
  }
  // #endif

  return statusHeight
}
