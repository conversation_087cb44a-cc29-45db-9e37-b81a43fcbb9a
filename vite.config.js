import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'] // 关闭警告
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    proxy: {
      // 代理 API 请求
      '/prod-api': {
        target: 'http://163.204.157.139:8082/prod-api',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/prod-api/, '')
      },
      // 代理 AI 相关请求
      '/prod-ai': {
        target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:8443',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/prod-ai/, '')
      },
      // 代理流式聊天请求
      '/stream-api': {
        target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:8443',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/stream-api/, '')
      }
    }
  }
})
